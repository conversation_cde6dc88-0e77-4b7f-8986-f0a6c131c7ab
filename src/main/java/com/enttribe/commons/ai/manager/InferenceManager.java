package com.enttribe.commons.ai.manager;

import com.enttribe.commons.ai.advisor.AuditAdvisor;
import com.enttribe.commons.ai.model.InferenceDetail;
import com.enttribe.commons.ai.model.ModelProviderConfig;
import com.enttribe.commons.ai.service.PromptApi;
import com.enttribe.commons.ai.util.AESUtils;
import com.enttribe.commons.ai.util.JsonUtils;
import io.micrometer.observation.ObservationRegistry;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.observation.ChatModelObservationConvention;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.observation.EmbeddingModelObservationConvention;
import org.springframework.ai.model.SimpleApiKey;
import org.springframework.ai.model.openai.autoconfigure.OpenAIAutoConfigurationUtil;
import org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties;
import org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties;
import org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties;
import org.springframework.ai.model.openai.autoconfigure.OpenAiParentProperties;
import org.springframework.ai.model.tool.DefaultToolExecutionEligibilityPredicate;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.model.tool.ToolExecutionEligibilityPredicate;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Manager class responsible for initializing, managing, and providing access to various AI inference models,
 * specifically Chat and Embedding models from different providers.
 *
 * <p>This class serves as a central hub for managing AI model instances across the application. It handles:
 * <ul>
 *     <li>Dynamic initialization of Chat and Embedding models from configured providers</li>
 *     <li>Management of model instances through internal maps</li>
 *     <li>Provider-specific model access and configuration</li>
 *     <li>Integration with Spring AI's OpenAI implementation</li>
 * </ul>
 * </p>
 *
 * <p>The class supports multiple AI providers and maintains separate registries for chat and embedding models.
 * It uses Spring's dependency injection and post-construction initialization to set up the required model instances.</p>
 *
 * <p>Key features:
 * <ul>
 *     <li>Thread-safe model instance management</li>
 *     <li>Support for multiple AI providers</li>
 *     <li>Integration with Spring AI's observation and metrics</li>
 *     <li>Automatic retry capabilities</li>
 *     <li>Custom error handling</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @see org.springframework.ai.chat.model.ChatModel
 * @see org.springframework.ai.embedding.EmbeddingModel
 * @see org.springframework.ai.openai.OpenAiChatModel
 * @see org.springframework.ai.openai.OpenAiEmbeddingModel
 * @since 1.0.0
 */
@Component
public class InferenceManager {

    @Value("${commons.ai.sdk.app.name}")
    private String applicationName;

    @Value("${commons.ai.sdk.fallback.inference.config:e30=}")
    private String fallbackInference;

    private static final Logger log = LoggerFactory.getLogger(InferenceManager.class);

    /**
     * Thread-safe map storing ChatModel instances keyed by provider name
     */
    private static volatile Map<String, ChatModel> chatModelMap = new HashMap<>();

    /**
     * Thread-safe map storing EmbeddingModel instances keyed by provider and model name combination
     */
    private static volatile Map<String, EmbeddingModel> embeddingModelMap = new HashMap<>();
    public static Map<String, RestClient> restClientMap = new HashMap<>();
    public static Map<String, WebClient> webClientMap = new HashMap<>();
    public static ModelProviderConfig fallbackProviderConfig;

    private final PromptApi promptApi;
    private final ToolCallingManager toolCallingManager;
    private final RetryTemplate retryTemplate;
    private final ResponseErrorHandler responseErrorHandler;
    private final OpenAiChatProperties chatProperties;
    private final OpenAiConnectionProperties commonProperties;
    private final OpenAiEmbeddingProperties embeddingProperties;
    private final ObjectProvider<RestClient.Builder> restClientBuilderProvider;
    private final ObjectProvider<WebClient.Builder> webClientBuilderProvider;
    private final ObjectProvider<ObservationRegistry> observationRegistry;
    private final ObjectProvider<ChatModelObservationConvention> chatModelObservationConventions;
    private final ObjectProvider<EmbeddingModelObservationConvention> embeddingModelObservationConventions;
    private final ObjectProvider<ToolExecutionEligibilityPredicate> openAiToolExecutionEligibilityPredicate;
    private final AuditAdvisor auditAdvisor;
    private final Advisor llmGuardAdvisor;

    /**
     * Constructs a new InferenceManager with all required dependencies.
     *
     * @param promptApi                            API client for retrieving prompt configurations
     * @param toolCallingManager                   the tool calling manager
     * @param retryTemplate                        Template for retry operations
     * @param responseErrorHandler                 Handler for API response errors
     * @param chatProperties                       OpenAI chat-specific properties
     * @param commonProperties                     Common OpenAI connection properties
     * @param embeddingProperties                  OpenAI embedding-specific properties
     * @param restClientBuilderProvider            Provider for RestClient builder
     * @param webClientBuilderProvider             Provider for WebClient builder
     * @param observationRegistry                  Registry for observations/metrics
     * @param chatModelObservationConventions      Conventions for chat model observations
     * @param embeddingModelObservationConventions Conventions for embedding model observations
     */
    public InferenceManager(Advisor llmGuardAdvisor, PromptApi promptApi, ToolCallingManager toolCallingManager,
                            RetryTemplate retryTemplate, ResponseErrorHandler responseErrorHandler, OpenAiChatProperties chatProperties,
                            OpenAiConnectionProperties commonProperties, OpenAiEmbeddingProperties embeddingProperties,
                            ObjectProvider<RestClient.Builder> restClientBuilderProvider,
                            ObjectProvider<WebClient.Builder> webClientBuilderProvider, ObjectProvider<ObservationRegistry> observationRegistry,
                            ObjectProvider<ChatModelObservationConvention> chatModelObservationConventions, ObjectProvider<EmbeddingModelObservationConvention> embeddingModelObservationConventions,
                            ObjectProvider<ToolExecutionEligibilityPredicate> openAiToolExecutionEligibilityPredicate, AuditAdvisor auditAdvisor
    ) {
        this.llmGuardAdvisor = llmGuardAdvisor;
        this.promptApi = promptApi;
        this.toolCallingManager = toolCallingManager;
        this.retryTemplate = retryTemplate;
        this.responseErrorHandler = responseErrorHandler;
        this.chatProperties = chatProperties;
        this.commonProperties = commonProperties;
        this.embeddingProperties = embeddingProperties;
        this.restClientBuilderProvider = restClientBuilderProvider;
        this.webClientBuilderProvider = webClientBuilderProvider;
        this.observationRegistry = observationRegistry;
        this.chatModelObservationConventions = chatModelObservationConventions;
        this.embeddingModelObservationConventions = embeddingModelObservationConventions;
        this.openAiToolExecutionEligibilityPredicate = openAiToolExecutionEligibilityPredicate;
        this.auditAdvisor = auditAdvisor;
    }


    private void prepareFallbackInferenceMap() {

        try {
            log.info("fallback inference map before : {}", fallbackInference);
            String decodedFallbackInference = new String(Base64.getDecoder().decode(fallbackInference));
            log.info("preparing fallback inference map for : {}", decodedFallbackInference);
            fallbackProviderConfig = JsonUtils.convertJsonToObject(decodedFallbackInference, ModelProviderConfig.class);
        } catch (Exception e) {
            log.error("error in parsing fallback inference map : {}", e.getMessage());
            fallbackProviderConfig = new ModelProviderConfig();
        }
    }

    private List<InferenceDetail> getLlmModelsByType(String type) {
        List<InferenceDetail> llmModels = promptApi.getLlmModelsByType(type);

        for (InferenceDetail inferenceDetail : llmModels) {
            try {
                log.debug("decrypting api key for provider : {}", inferenceDetail.getProvider());
                String decryptedApiKey = AESUtils.decrypt(inferenceDetail.getApiKey());
                inferenceDetail.setApiKey(decryptedApiKey);
                log.debug("api key decrypted for provider : {}", inferenceDetail.getProvider());
            } catch (Exception e) {
                log.error("failed to decrypt api key for provider : {}", inferenceDetail.getProvider(), e);
                throw new RuntimeException(e);
            }
        }

        return llmModels;
    }

    private List<InferenceDetail> getLlmModels(String applicationName) {
        List<InferenceDetail> llmModels = promptApi.getLlmModels(applicationName);

        for (InferenceDetail inferenceDetail : llmModels) {
            try {
                log.debug("decrypting api key for provider : {}", inferenceDetail.getProvider());
                String decryptedApiKey = AESUtils.decrypt(inferenceDetail.getApiKey());
                inferenceDetail.setApiKey(decryptedApiKey);
                log.debug("api key decrypted for provider : {}", inferenceDetail.getProvider());
            } catch (Exception e) {
                log.error("failed to decrypt api key for provider : {}", inferenceDetail.getProvider(), e);
                throw new RuntimeException(e);
            }
        }

        return llmModels;
    }

    /**
     * Retrieves a ChatModel instance for the specified provider.
     *
     * @param provider The name of the AI provider
     * @return The ChatModel instance for the specified provider, or null if not found
     */
    public ChatModel getChatModelByProvider(String provider) {
        log.debug("chat model map keys : {} and provider is : {}", chatModelMap.keySet(), provider);
        return chatModelMap.get(provider);
    }

    /**
     * Retrieves a ChatClient instance configured with the ChatModel for the specified provider.
     * The returned ChatClient includes default advisors for LLM guard functionality.
     *
     * @param provider The name of the AI provider
     * @return A configured ChatClient instance for the specified provider
     */
    public ChatClient getChatClientByProvider(String provider) {
        log.debug("chat model map keys : {} and provider is : {}", chatModelMap.keySet(), provider);
        ChatModel chatModel = chatModelMap.get(provider);
        return ChatClient.builder(chatModel)
                .defaultAdvisors(llmGuardAdvisor, auditAdvisor)
                .build();
    }

    /**
     * Retrieves a ChatClient instance configured with the ChatModel for the specified provider.
     * The returned ChatClient includes default advisors for LLM guard functionality based on allowGuard.
     *
     * @param provider The name of the AI provider
     * @return A configured ChatClient instance for the specified provider
     */
    public ChatClient getChatClientByProvider(String provider, boolean allowGuard) {
        log.debug("chat model map keys : {} allowGuard : {}", chatModelMap.keySet(), allowGuard);
        ChatModel chatModel = chatModelMap.get(provider);
        if (!allowGuard) {
            return ChatClient.builder(chatModel)
                    .defaultAdvisors(auditAdvisor)
                    .build();
        }
        return ChatClient.builder(chatModel)
                .defaultAdvisors(llmGuardAdvisor, auditAdvisor)
                .build();
    }

    /**
     * Retrieves an EmbeddingModel instance for the specified provider and model combination.
     *
     * @param key The combined key in the format "provider_model"
     * @return The EmbeddingModel instance for the specified key, or null if not found
     */
    public EmbeddingModel getEmbeddingModel(String key) {
        log.info("embedding model map keys : {}", embeddingModelMap.keySet());
        return embeddingModelMap.get(key);
    }

    /**
     * Initializes all chat and embedding models during application startup.
     * This method is automatically called after dependency injection is complete.
     * It fetches model configurations from the prompt API and creates corresponding model instances.
     * Thread-safe initialization is ensured through the use of volatile maps.
     */
    @PostConstruct
    public void initializeChatModels() {
        prepareFallbackInferenceMap();
        log.info("Initializing chat models for application: {}", applicationName);

        log.info("chat models are (before) : {}", chatModelMap.keySet());
        // Fetch LLM models for the application
        List<InferenceDetail> inferenceDetails = getLlmModels(applicationName);
        log.info("total {} chat models are fetched", inferenceDetails.size());

        Map<String, ChatModel> chatModelTempMap = new HashMap<>();

        // Iterate through each inference
        for (InferenceDetail inferenceDetail : inferenceDetails) {
            log.info("registering chat model for provider : {}", inferenceDetail.getProvider());
            try {
                // Create OpenAiChatModel instance
                OpenAiChatModel openAiChatModel = openAiChatModel(inferenceDetail, commonProperties, chatProperties,
                        restClientBuilderProvider, webClientBuilderProvider, toolCallingManager, retryTemplate,
                        responseErrorHandler, observationRegistry, chatModelObservationConventions, openAiToolExecutionEligibilityPredicate);

                // Populate the map
                String providerKey = inferenceDetail.getProvider();
                log.info("adding chat model for key {}", providerKey);
                chatModelTempMap.put(providerKey, openAiChatModel);
                log.info("chat model initialized for : {}", providerKey);
            } catch (Exception e) {
                log.error("failed to initialize chat model for provider : {}", inferenceDetail.getProvider(), e);
            }
        }

        if (!chatModelTempMap.isEmpty()) {
            log.info("updating chat model map");
            InferenceManager.chatModelMap = Map.copyOf(chatModelTempMap);
        } else {
            log.warn("chat model map is empty");
        }
        log.info("chat models are (after) : {}", chatModelMap.keySet());

        log.info("embedding models are (before) : {}", embeddingModelMap.keySet());
        // Fetch embedding models
        List<InferenceDetail> embeddingModelDetails = getLlmModelsByType("embedding");
        log.info("total {} embedding models are fetched", embeddingModelDetails.size());

        Map<String, EmbeddingModel> embeddingModelTempMap = new HashMap<>();

        for (InferenceDetail inferenceDetail : embeddingModelDetails) {
            log.info("registering embedding model for provider : {} and model : {}", inferenceDetail.getProvider(), inferenceDetail.getModel());
            try {
                // Create OpenAiEmbeddingModel instance
                OpenAiEmbeddingModel openAiEmbeddingModel = openAiEmbeddingModel(inferenceDetail, commonProperties, embeddingProperties,
                        restClientBuilderProvider, webClientBuilderProvider,
                        retryTemplate, responseErrorHandler, observationRegistry, embeddingModelObservationConventions);

                // Populate the map
                String key = String.format("%s_%s", inferenceDetail.getProvider(), inferenceDetail.getModel());
                log.info("adding embedding model for key {}", key);
                embeddingModelTempMap.put(key, openAiEmbeddingModel);

                log.info("embedding model initialized for : {}", key);
            } catch (Exception e) {
                log.error("failed to initialize embedding model for provider : {}",
                        inferenceDetail.getProvider() + "_" + inferenceDetail.getModel(), e);
            }
        }
        if (!embeddingModelTempMap.isEmpty()) {
            log.info("updating embedding model map");
            InferenceManager.embeddingModelMap = Map.copyOf(embeddingModelTempMap);
        } else {
            log.warn("embedding model map is empty");
        }
        log.info("embedding models are (after) : {}", embeddingModelMap.keySet());

        log.info("model initialization complete.");
    }

    public OpenAiChatModel openAiChatModel(InferenceDetail inferenceDetail, OpenAiConnectionProperties commonProperties,
                                           OpenAiChatProperties chatProperties, ObjectProvider<RestClient.Builder> restClientBuilderProvider,
                                           ObjectProvider<WebClient.Builder> webClientBuilderProvider, ToolCallingManager toolCallingManager,
                                           RetryTemplate retryTemplate, ResponseErrorHandler responseErrorHandler,
                                           ObjectProvider<ObservationRegistry> observationRegistry,
                                           ObjectProvider<ChatModelObservationConvention> observationConvention,
                                           ObjectProvider<ToolExecutionEligibilityPredicate> openAiToolExecutionEligibilityPredicate) {

        chatProperties.setApiKey(inferenceDetail.getApiKey());
        chatProperties.setBaseUrl(inferenceDetail.getBaseUrl());

        var openAiApi = openAiApi(inferenceDetail, chatProperties, commonProperties,
                restClientBuilderProvider.getIfAvailable(RestClient::builder),
                webClientBuilderProvider.getIfAvailable(WebClient::builder), responseErrorHandler, "chat");

        var chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(chatProperties.getOptions())
                .toolCallingManager(toolCallingManager)
                .toolExecutionEligibilityPredicate(openAiToolExecutionEligibilityPredicate
                        .getIfUnique(() -> new DefaultToolExecutionEligibilityPredicate()))
                .retryTemplate(retryTemplate)
                .observationRegistry(observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP))
                .build();

        observationConvention.ifAvailable(chatModel::setObservationConvention);

        return chatModel;
    }

    public OpenAiEmbeddingModel openAiEmbeddingModel(InferenceDetail inferenceDetail, OpenAiConnectionProperties commonProperties,
                                                     OpenAiEmbeddingProperties embeddingProperties, ObjectProvider<RestClient.Builder> restClientBuilderProvider,
                                                     ObjectProvider<WebClient.Builder> webClientBuilderProvider, RetryTemplate retryTemplate,
                                                     ResponseErrorHandler responseErrorHandler, ObjectProvider<ObservationRegistry> observationRegistry,
                                                     ObjectProvider<EmbeddingModelObservationConvention> observationConvention) {

        embeddingProperties.setApiKey(inferenceDetail.getApiKey());
        embeddingProperties.setBaseUrl(inferenceDetail.getBaseUrl());
        embeddingProperties.setOptions(OpenAiEmbeddingOptions.builder().model(inferenceDetail.getModel()).build());

        var openAiApi = openAiApi(inferenceDetail, embeddingProperties, commonProperties,
                restClientBuilderProvider.getIfAvailable(RestClient::builder),
                webClientBuilderProvider.getIfAvailable(WebClient::builder), responseErrorHandler, "embedding");

        var embeddingModel = new OpenAiEmbeddingModel(openAiApi, embeddingProperties.getMetadataMode(),
                embeddingProperties.getOptions(), retryTemplate,
                observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP));

        observationConvention.ifAvailable(embeddingModel::setObservationConvention);

        return embeddingModel;
    }

    private OpenAiApi openAiApi(InferenceDetail inferenceDetail, OpenAiChatProperties chatProperties, OpenAiConnectionProperties commonProperties,
                                RestClient.Builder restClientBuilder, WebClient.Builder webClientBuilder,
                                ResponseErrorHandler responseErrorHandler, String modelType) {

        OpenAIAutoConfigurationUtil.ResolvedConnectionProperties resolved = resolveConnectionProperties(
                commonProperties, chatProperties, modelType);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("api-key", inferenceDetail.getApiKey());
        Consumer<HttpHeaders> finalHeaders = h -> {
            h.setBearerAuth(inferenceDetail.getApiKey());
            h.setContentType(MediaType.APPLICATION_JSON);
            h.addAll(headers);
        };
        RestClient restClient = restClientBuilder.clone()
                .baseUrl(inferenceDetail.getBaseUrl())
                .defaultHeaders(finalHeaders)
                .defaultStatusHandler(responseErrorHandler)
                .build();

        WebClient webClient = webClientBuilder.clone()
                .baseUrl(inferenceDetail.getBaseUrl())
                .defaultHeaders(finalHeaders)
                .build();

        InferenceManager.restClientMap.put(inferenceDetail.getProvider(), restClient);
        InferenceManager.webClientMap.put(inferenceDetail.getProvider(), webClient);
//        InferenceManager.fallbackInferenceMap.put(inferenceDetail.getProvider(), inferenceDetail.getBaseUrl());

        if (inferenceDetail.getProvider().equals("azure")) {
            return OpenAiApi.builder()
                    .baseUrl(inferenceDetail.getBaseUrl())
                    .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
                    .headers(resolved.headers())
                    .completionsPath("")
                    .embeddingsPath(OpenAiEmbeddingProperties.DEFAULT_EMBEDDINGS_PATH)
                    .restClientBuilder(restClientBuilder)
                    .webClientBuilder(webClientBuilder)
                    .responseErrorHandler(responseErrorHandler)
                    .fallBackConfig(fallbackProviderConfig.getProviderByName(inferenceDetail.getProvider()))
                    .build();
        } else if (inferenceDetail.getProvider().equals("google")) {
            return OpenAiApi.builder()
                    .baseUrl(inferenceDetail.getBaseUrl())
                    .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
                    .headers(resolved.headers())
                    .completionsPath("/v1beta/openai/chat/completions")
                    .embeddingsPath(OpenAiEmbeddingProperties.DEFAULT_EMBEDDINGS_PATH)
                    .restClientBuilder(restClientBuilder)
                    .webClientBuilder(webClientBuilder)
                    .responseErrorHandler(responseErrorHandler)
                    .fallBackConfig(fallbackProviderConfig.getProviderByName(inferenceDetail.getProvider()))
                    .build();
        } else {
            return OpenAiApi.builder()
                    .baseUrl(inferenceDetail.getBaseUrl())
                    .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
                    .headers(resolved.headers())
                    .completionsPath(chatProperties.getCompletionsPath())
                    .embeddingsPath(OpenAiEmbeddingProperties.DEFAULT_EMBEDDINGS_PATH)
                    .restClientBuilder(restClientBuilder)
                    .webClientBuilder(webClientBuilder)
                    .responseErrorHandler(responseErrorHandler)
                    .fallBackConfig(fallbackProviderConfig.getProviderByName(inferenceDetail.getProvider()))
                    .build();
        }
    }

    private OpenAiApi openAiApi(InferenceDetail inferenceDetail, OpenAiEmbeddingProperties embeddingProperties,
                                OpenAiConnectionProperties commonProperties, RestClient.Builder restClientBuilder,
                                WebClient.Builder webClientBuilder, ResponseErrorHandler responseErrorHandler, String modelType) {

        OpenAIAutoConfigurationUtil.ResolvedConnectionProperties resolved = resolveConnectionProperties(
                commonProperties, embeddingProperties, modelType);

        return OpenAiApi.builder()
                .baseUrl(inferenceDetail.getBaseUrl())
                .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
                .headers(resolved.headers())
                .completionsPath(OpenAiChatProperties.DEFAULT_COMPLETIONS_PATH)
                .embeddingsPath(embeddingProperties.getEmbeddingsPath())
                .restClientBuilder(restClientBuilder)
                .webClientBuilder(webClientBuilder)
                .responseErrorHandler(responseErrorHandler)
                .build();
    }

    protected static OpenAIAutoConfigurationUtil.ResolvedConnectionProperties resolveConnectionProperties(
            OpenAiParentProperties commonProperties, OpenAiParentProperties modelProperties, String modelType) {

        String baseUrl = StringUtils.hasText(modelProperties.getBaseUrl()) ? modelProperties.getBaseUrl()
                : commonProperties.getBaseUrl();
        String apiKey = StringUtils.hasText(modelProperties.getApiKey()) ? modelProperties.getApiKey()
                : commonProperties.getApiKey();
        String projectId = StringUtils.hasText(modelProperties.getProjectId()) ? modelProperties.getProjectId()
                : commonProperties.getProjectId();
        String organizationId = StringUtils.hasText(modelProperties.getOrganizationId())
                ? modelProperties.getOrganizationId() : commonProperties.getOrganizationId();

        Map<String, List<String>> connectionHeaders = new HashMap<>();
        if (StringUtils.hasText(projectId)) {
            connectionHeaders.put("OpenAI-Project", List.of(projectId));
        }
        if (StringUtils.hasText(organizationId)) {
            connectionHeaders.put("OpenAI-Organization", List.of(organizationId));
        }

        Assert.hasText(baseUrl,
                "OpenAI base URL must be set.  Use the connection property: spring.ai.openai.base-url or spring.ai.openai."
                        + modelType + ".base-url property.");
        Assert.hasText(apiKey,
                "OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai."
                        + modelType + ".api-key property.");

        return new OpenAIAutoConfigurationUtil.ResolvedConnectionProperties(baseUrl, apiKey, CollectionUtils.toMultiValueMap(connectionHeaders));
    }

    public record ResolvedConnectionProperties(String baseUrl, String apiKey, MultiValueMap<String, String> headers) {

    }

}
